<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSS Test Page</title>
  <link rel="stylesheet" href="/styles.css?v=1.0">
  <style>
    .test-indicator {
      background: #ff0000;
      color: white;
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
    }
    .css-loaded .test-indicator {
      background: #00ff00;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>CSS Loading Test</h1>
    <div class="test-indicator">
      If this is GREEN, CSS is loading properly. If RED, there's an issue.
    </div>
    
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Test Card</h2>
      </div>
      <p>This card should have proper styling if CSS is loaded.</p>
    </div>
    
    <div class="stats">
      <div class="stat-card primary">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <div class="stat-number">100</div>
          <div class="stat-label">Test Stat</div>
        </div>
      </div>
    </div>
    
    <div class="btn-group">
      <a href="#" class="btn btn-primary">Primary Button</a>
      <a href="#" class="btn btn-secondary">Secondary Button</a>
      <a href="/" class="btn btn-outline">Back to Home</a>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check if CSS custom properties are supported and loaded
      const testElement = document.createElement('div');
      testElement.className = 'header';
      document.body.appendChild(testElement);
      
      const computedStyle = window.getComputedStyle(testElement);
      const hasBackground = computedStyle.background && computedStyle.background !== 'initial';
      
      document.body.removeChild(testElement);
      
      if (hasBackground) {
        document.body.classList.add('css-loaded');
        console.log('✅ CSS loaded successfully');
      } else {
        console.log('❌ CSS not loaded properly');
      }
    });
  </script>
</body>
</html>
