{"name": "ai-image-generator", "version": "1.0.0", "description": "Node.js application that autonomously generates images using OpenAI's gpt-image-1 model", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.6.7", "cloudinary": "^1.41.0", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "fs-extra": "^11.2.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "openai": "^4.28.0", "sharp": "^0.34.3", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.3"}, "keywords": ["openai", "image-generation", "cloudinary", "node.js", "express"], "author": "", "license": "MIT"}