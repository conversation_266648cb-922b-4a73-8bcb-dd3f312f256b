/* AI Image Generator - Beautiful Modern Design */
:root {
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --primary-light: #e0e7ff;
  --secondary-color: #10b981;
  --secondary-hover: #059669;
  --secondary-light: #d1fae5;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --danger-light: #fee2e2;
  --warning-color: #f59e0b;
  --warning-hover: #d97706;
  --warning-light: #fef3c7;
  --info-color: #3b82f6;
  --info-hover: #2563eb;
  --info-light: #dbeafe;
  --success-color: #10b981;
  --success-light: #d1fae5;
  
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --card-background: #ffffff;
  --surface-1: #f9fafb;
  --surface-2: #f3f4f6;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-inverse: #ffffff;
  --border-color: #e5e7eb;
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --transition: 0.25s ease-out;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--background);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  min-height: 100vh;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 24px 0;
  margin-bottom: 48px;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 24px;
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all var(--transition);
}

.logo:hover {
  transform: translateY(-2px);
}

.nav {
  display: flex;
  gap: 4px;
  background: var(--surface-1);
  padding: 4px;
  border-radius: var(--border-radius-lg);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  transition: all var(--transition);
  font-weight: 500;
  font-size: 14px;
}

.nav-link:hover,
.nav-link.active {
  background: var(--primary-color);
  color: var(--text-inverse);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

/* Dashboard */
.dashboard-header {
  text-align: center;
  margin-bottom: 48px;
  padding: 48px 0;
}

.dashboard-title {
  font-size: 48px;
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.dashboard-description {
  font-size: 18px;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Stats */
.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

.stat-card {
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: 32px;
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  transition: all var(--transition);
  display: flex;
  align-items: center;
  gap: 24px;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  opacity: 0;
  transition: opacity var(--transition);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-icon {
  font-size: 48px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-lg);
  background: var(--surface-1);
  flex-shrink: 0;
}

.stat-card.primary .stat-icon {
  background: var(--primary-light);
  color: var(--primary-color);
}

.stat-card.success .stat-icon {
  background: var(--secondary-light);
  color: var(--secondary-color);
}

.stat-card.info .stat-icon {
  background: var(--info-light);
  color: var(--info-color);
}

.stat-card.warning .stat-icon {
  background: var(--warning-light);
  color: var(--warning-color);
}

.stat-number {
  font-size: 48px;
  font-weight: 800;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 16px;
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-content {
  flex: 1;
}

.stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
}

.trend-indicator {
  font-size: 18px;
  font-weight: 700;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  background: var(--surface-1);
}

.trend-indicator.positive {
  color: var(--secondary-color);
  background: var(--secondary-light);
}

.trend-indicator.negative {
  color: var(--danger-color);
  background: var(--danger-light);
}

.trend-indicator.neutral {
  color: var(--text-secondary);
  background: var(--surface-2);
}

/* Cards */
.card {
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: 32px;
  margin-bottom: 32px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--surface-2);
}

.card-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: var(--shadow);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: var(--text-inverse);
  border: 1px solid var(--primary-color);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-hover) 100%);
  color: var(--text-inverse);
  border: 1px solid var(--secondary-color);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
  color: var(--text-inverse);
  border: 1px solid var(--danger-color);
}

.btn-outline {
  background: var(--card-background);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
}

.btn-outline:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-sm {
  padding: 8px 16px;
  font-size: 14px;
}

/* Grid */
.grid {
  display: grid;
  gap: 24px;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Prompt Cards */
.prompt-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all var(--transition);
  background: var(--card-background);
}

.prompt-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.prompt-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  background: var(--surface-1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: 24px;
}

.prompt-content {
  padding: 16px;
}

.prompt-text {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: 12px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prompt-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  font-size: 12px;
  color: var(--text-secondary);
}

.prompt-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
}

.badge-success {
  background: var(--success-light);
  color: var(--success-color);
}

.badge-danger {
  background: var(--danger-light);
  color: var(--danger-color);
}

.badge-info {
  background: var(--info-light);
  color: var(--info-color);
}

/* History Items */
.history-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  transition: all var(--transition);
}

.history-item:hover {
  background: var(--surface-1);
}

.history-item:last-child {
  border-bottom: none;
}

.history-image {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius);
  object-fit: cover;
  background: var(--surface-1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.history-content {
  flex: 1;
}

.history-prompt {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.history-meta {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Forms */
.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-primary);
}

.form-textarea,
.form-input {
  width: 100%;
  padding: 12px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 16px;
  transition: all var(--transition);
  background: var(--card-background);
  resize: vertical;
}

.form-textarea:focus,
.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light);
  outline: none;
}

.form-file {
  border: 3px dashed var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 48px;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition);
  background: var(--surface-1);
}

.form-file:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

/* Utilities */
.text-center { text-align: center; }
.text-secondary { color: var(--text-secondary) !important; }
.text-primary { color: var(--text-primary) !important; }
.hidden { display: none !important; }
.mb-2 { margin-bottom: 8px !important; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: 8px; }

/* Spinner */
.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Alert */
.alert {
  padding: 16px;
  border-radius: var(--border-radius);
  margin-bottom: 16px;
  border: 1px solid transparent;
}

.alert-success {
  background-color: var(--success-light);
  border-color: var(--success-color);
  color: var(--success-color);
}

.alert-error {
  background-color: var(--danger-light);
  border-color: var(--danger-color);
  color: var(--danger-color);
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: 48px;
  padding: 48px 0;
}

.page-title {
  font-size: 48px;
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.page-description {
  font-size: 18px;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Method Tabs */
.method-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 48px;
  background: var(--surface-1);
  padding: 8px;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.method-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 24px;
  border: none;
  border-radius: var(--border-radius);
  background: transparent;
  color: var(--text-secondary);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition);
  position: relative;
}

.method-tab.active {
  background: var(--card-background);
  color: var(--primary-color);
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.tab-icon {
  font-size: 18px;
}

/* Method Content */
.method-content {
  display: none;
}

.method-content.active {
  display: block;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Form Elements */
.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.label-icon {
  font-size: 18px;
}

.form-textarea.enhanced,
.form-input.enhanced {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 16px;
  font-size: 16px;
  transition: all var(--transition);
  background: var(--card-background);
  resize: vertical;
}

.form-textarea.enhanced:focus,
.form-input.enhanced:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light);
  outline: none;
  transform: translateY(-1px);
}

.form-help {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 12px 16px;
  background: var(--info-light);
  border-radius: var(--border-radius);
  font-size: 14px;
  color: var(--info-color);
}

.help-icon {
  font-size: 16px;
}

/* Enhanced File Upload */
.form-file.enhanced {
  border: 3px dashed var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 48px;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition);
  background: var(--surface-1);
  position: relative;
  overflow: hidden;
}

.form-file.enhanced:hover,
.form-file.enhanced.drag-over {
  border-color: var(--primary-color);
  background: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.file-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.file-upload-icon {
  font-size: 48px;
  opacity: 0.7;
}

.file-upload-text {
  font-size: 18px;
  color: var(--text-primary);
}

.file-upload-formats {
  font-size: 14px;
  color: var(--text-secondary);
}

/* Enhanced Checkbox */
.form-checkbox.enhanced {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--surface-1);
  border-radius: var(--border-radius);
  border: 2px solid var(--border-color);
  transition: all var(--transition);
}

.form-checkbox.enhanced:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  cursor: pointer;
  margin: 0;
}

.checkbox-icon {
  font-size: 18px;
}

/* Responsive */
@media (max-width: 768px) {
  .container { padding: 0 16px; }
  .header-content { flex-direction: column; gap: 16px; }
  .nav { flex-wrap: wrap; justify-content: center; }
  .grid-2, .grid-3 { grid-template-columns: 1fr; }
  .stats { grid-template-columns: repeat(2, 1fr); }
  .dashboard-title { font-size: 36px; flex-direction: column; gap: 8px; }
  .stat-card { flex-direction: column; text-align: center; }
  .prompt-actions { flex-direction: column; }
  .method-tabs { flex-direction: column; }
  .page-title { font-size: 36px; flex-direction: column; gap: 8px; }
}

/* Image Preview */
.image-preview {
  margin-top: 24px;
  padding: 24px;
  background: var(--surface-1);
  border-radius: var(--border-radius-lg);
  border: 2px solid var(--border-color);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--border-color);
}

.preview-title {
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-image {
  width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

/* JSON Preview */
.json-preview {
  margin-top: 24px;
  padding: 24px;
  background: var(--surface-1);
  border-radius: var(--border-radius-lg);
  border: 2px solid var(--border-color);
}

.json-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  padding: 16px;
  background: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  color: var(--primary-color);
  font-weight: 700;
}

.json-content {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
  color: var(--text-primary);
  white-space: pre-wrap;
}

/* JSON Format Help */
.json-format-help {
  margin-top: 24px;
  padding: 24px;
  background: var(--warning-light);
  border-radius: var(--border-radius-lg);
  border-left: 4px solid var(--warning-color);
}

.help-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.json-format-help h4 {
  margin: 0;
  color: var(--warning-color);
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.format-notes {
  margin-top: 16px;
  padding: 16px;
  background: var(--card-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.format-notes p {
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-primary);
}

.format-notes ul {
  margin: 0;
  padding-left: 24px;
  color: var(--text-secondary);
}

.format-notes li {
  margin-bottom: 4px;
  line-height: 1.5;
}

.json-example {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  margin: 0;
  overflow-x: auto;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 16px;
  margin-top: 48px;
  padding-top: 24px;
  border-top: 2px solid var(--surface-2);
}

/* Button Ghost Style */
.btn-ghost {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  box-shadow: none;
}

.btn-ghost:hover {
  background: var(--surface-1);
  color: var(--text-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 24px;
  right: 24px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toast {
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: 16px 24px;
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--primary-color);
  min-width: 300px;
  max-width: 400px;
  display: flex;
  align-items: center;
  gap: 16px;
  animation: slideInRight 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.toast.success {
  border-left-color: var(--success-color);
}

.toast.error {
  border-left-color: var(--danger-color);
}

.toast.warning {
  border-left-color: var(--warning-color);
}

.toast.info {
  border-left-color: var(--info-color);
}

.toast-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.toast.success .toast-icon {
  color: var(--success-color);
}

.toast.error .toast-icon {
  color: var(--danger-color);
}

.toast.warning .toast-icon {
  color: var(--warning-color);
}

.toast.info .toast-icon {
  color: var(--info-color);
}

.toast-content {
  flex: 1;
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--border-radius);
  transition: all 0.15s;
  flex-shrink: 0;
}

.toast-close:hover {
  background: var(--surface-1);
  color: var(--text-primary);
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: var(--primary-color);
  animation: toastProgress 5s linear forwards;
}

.toast.success .toast-progress {
  background: var(--success-color);
}

.toast.error .toast-progress {
  background: var(--danger-color);
}

.toast.warning .toast-progress {
  background: var(--warning-color);
}

.toast.info .toast-progress {
  background: var(--info-color);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes toastProgress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
