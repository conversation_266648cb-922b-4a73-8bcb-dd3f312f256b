const OpenAI = require('openai');
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class OpenAIService {
  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async generateImage(prompt, referenceImagePath = null) {
    try {
      console.log('🎨 Generating image with prompt:', prompt);

      // If reference image is provided, use the edit endpoint for multimodal input
      if (referenceImagePath && await fs.pathExists(referenceImagePath)) {
        console.log('📸 Using reference image:', referenceImagePath);
        return await this.generateWithReferenceImage(prompt, referenceImagePath);
      }

      // Standard text-only generation
      const requestBody = {
        model: 'gpt-image-1',
        prompt: prompt,
        n: 1,
        size: process.env.IMAGE_SIZE || '1024x1024',
        quality: process.env.IMAGE_QUALITY || 'medium',
        moderation: process.env.MODERATION_LEVEL || 'auto',
        output_format: process.env.IMAGE_FORMAT || 'png'
      };

      console.log('📋 Request body:', JSON.stringify(requestBody, null, 2));

      const response = await this.client.images.generate(requestBody);
      
      if (response.data && response.data.length > 0) {
        const imageData = response.data[0];
        const filename = `generated_${uuidv4()}.${process.env.IMAGE_FORMAT || 'png'}`;
        const filepath = path.join(__dirname, '..', 'generated-images', filename);
        
        // Decode base64 and save image
        const buffer = Buffer.from(imageData.b64_json, 'base64');
        await fs.writeFile(filepath, buffer);
        
        console.log('✅ Image generated and saved:', filename);
        
        return {
          success: true,
          filename: filename,
          filepath: filepath,
          usage: response.usage || null
        };
      } else {
        throw new Error('No image data received from OpenAI');
      }
    } catch (error) {
      console.error('❌ OpenAI generation error:', error);
      throw error;
    }
  }

  async generateWithReferenceImage(prompt, referenceImagePath) {
    try {
      console.log('🖼️ Generating image with reference:', referenceImagePath);

      // Read the reference image
      let imageBuffer = await fs.readFile(referenceImagePath);
      const mimeType = this.getMimeType(referenceImagePath);

      console.log('📸 Reference image loaded, size:', imageBuffer.length, 'bytes');
      console.log('🎨 MIME type:', mimeType);

      // Check if image is too large for OpenAI endpoints (max ~16KB)
      if (imageBuffer.length > 16384) {
        console.log('⚠️ Image too large for OpenAI endpoints, resizing...');
        console.log('📊 Original size:', imageBuffer.length, 'bytes');
        try {
          imageBuffer = await this.resizeImageForOpenAI(imageBuffer);
          console.log('✅ Resized image size:', imageBuffer.length, 'bytes');
        } catch (resizeError) {
          console.error('❌ Image resizing failed:', resizeError.message);
          console.log('🔄 Continuing with original image (will likely fail)');
        }
      } else {
        console.log('✅ Image size OK:', imageBuffer.length, 'bytes');
      }

      // Create a File-like object for the OpenAI API (Node.js compatible)
      const { Readable } = require('stream');
      const imageStream = Readable.from(imageBuffer);
      imageStream.path = 'reference.jpg'; // Add filename
      imageStream.type = mimeType; // Add MIME type

      // APPROACH 1: Try using images.variations endpoint for reference-based generation
      console.log('🔄 Trying images.variations endpoint...');

      try {
        const variationResponse = await this.client.images.createVariation({
          model: 'gpt-image-1',
          image: imageStream,
          prompt: `Create a variation of this image: ${prompt}`,
          n: 1,
          size: process.env.IMAGE_SIZE || '1024x1024',
          quality: process.env.IMAGE_QUALITY || 'medium',
          output_format: process.env.IMAGE_FORMAT || 'png'
        });

        if (variationResponse.data && variationResponse.data.length > 0) {
          const imageData = variationResponse.data[0];
          const filename = `generated_${uuidv4()}.${process.env.IMAGE_FORMAT || 'png'}`;
          const filepath = path.join(__dirname, '..', 'generated-images', filename);

          const buffer = Buffer.from(imageData.b64_json, 'base64');
          await fs.writeFile(filepath, buffer);

          console.log('✅ Image generated with variations endpoint and saved:', filename);

          return {
            success: true,
            filename: filename,
            filepath: filepath,
            usage: variationResponse.usage || null,
            usedReferenceImage: true,
            method: 'variations'
          };
        }
      } catch (variationError) {
        console.log('⚠️ Variations endpoint failed:', variationError.message);
      }

      // APPROACH 2: Try using images.edit endpoint
      console.log('🔄 Trying images.edit endpoint...');

      try {
        const editResponse = await this.client.images.edit({
          model: 'gpt-image-1',
          image: imageStream,
          prompt: `Transform this image: ${prompt}`,
          n: 1,
          size: process.env.IMAGE_SIZE || '1024x1024',
          quality: process.env.IMAGE_QUALITY || 'medium',
          output_format: process.env.IMAGE_FORMAT || 'png'
        });

        if (editResponse.data && editResponse.data.length > 0) {
          const imageData = editResponse.data[0];
          const filename = `generated_${uuidv4()}.${process.env.IMAGE_FORMAT || 'png'}`;
          const filepath = path.join(__dirname, '..', 'generated-images', filename);

          const buffer = Buffer.from(imageData.b64_json, 'base64');
          await fs.writeFile(filepath, buffer);

          console.log('✅ Image generated with edit endpoint and saved:', filename);

          return {
            success: true,
            filename: filename,
            filepath: filepath,
            usage: editResponse.usage || null,
            usedReferenceImage: true,
            method: 'edit'
          };
        }
      } catch (editError) {
        console.log('⚠️ Edit endpoint failed:', editError.message);
      }

      // If all specific approaches fail, throw error to trigger fallback
      throw new Error('All reference image approaches failed');
    } catch (error) {
      console.error('❌ Error generating image with multimodal input:', error);

      // Fallback 1: Try using the images.edit endpoint
      try {
        console.log('🔄 Trying images.edit endpoint as fallback...');

        const imageBuffer = await fs.readFile(referenceImagePath);

        const response = await this.client.images.edit({
          model: 'gpt-image-1',
          image: imageBuffer,
          prompt: `Transform this image: ${prompt}`,
          n: 1,
          size: process.env.IMAGE_SIZE || '1024x1024',
          quality: process.env.IMAGE_QUALITY || 'medium',
          moderation: process.env.MODERATION_LEVEL || 'auto',
          output_format: process.env.IMAGE_FORMAT || 'png'
        });

        if (response.data && response.data.length > 0) {
          const imageData = response.data[0];
          const filename = `generated_${uuidv4()}.${process.env.IMAGE_FORMAT || 'png'}`;
          const filepath = path.join(__dirname, '..', 'generated-images', filename);

          const buffer = Buffer.from(imageData.b64_json, 'base64');
          await fs.writeFile(filepath, buffer);

          console.log('✅ Image generated with edit endpoint and saved:', filename);

          return {
            success: true,
            filename: filename,
            filepath: filepath,
            usage: response.usage || null,
            usedReferenceImage: true,
            method: 'edit'
          };
        }
      } catch (editError) {
        console.error('❌ Edit endpoint also failed:', editError);
      }

      // Fallback 2: Enhanced text-only generation
      console.log('🔄 Falling back to enhanced text-only generation...');
      const fallbackPrompt = `Create an image inspired by the style and composition of the reference image. ${prompt}. Use similar visual elements, color palette, and artistic approach.`;
      return await this.generateImage(fallbackPrompt);
    }
  }

  async generateImageFromPromptData(promptData) {
    try {
      console.log('🎯 Generating image from prompt data:', {
        text: promptData.text,
        hasReferenceImage: !!promptData.referenceImage,
        referenceImage: promptData.referenceImage
      });

      let enhancedPrompt = promptData.text;

      if (promptData.referenceImage) {
        const referenceImagePath = path.join(__dirname, '..', 'uploads', promptData.referenceImage);

        // Check if reference image exists
        if (await fs.pathExists(referenceImagePath)) {
          console.log('📸 Reference image found, using multimodal generation');
          return await this.generateImage(enhancedPrompt, referenceImagePath);
        } else {
          console.log('⚠️ Reference image not found, falling back to text-only generation');
          enhancedPrompt = `Create an image inspired by the concept of "${promptData.referenceImage}": ${promptData.text}`;
          return await this.generateImage(enhancedPrompt);
        }
      } else {
        console.log('📝 Text-only generation');
        return await this.generateImage(enhancedPrompt);
      }
    } catch (error) {
      console.error('❌ Error generating image from prompt data:', error);
      throw error;
    }
  }

  // Helper method to get MIME type from file extension
  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.webp': 'image/webp',
      '.gif': 'image/gif'
    };
    return mimeTypes[ext] || 'image/jpeg';
  }

  // Helper method to resize image for OpenAI API compatibility
  async resizeImageForOpenAI(imageBuffer) {
    try {
      // Try to use sharp for high-quality resizing
      const sharp = require('sharp');

      // Resize to a smaller dimension that should result in <16KB
      const resizedBuffer = await sharp(imageBuffer)
        .resize(256, 256, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .jpeg({ quality: 60 }) // Use JPEG with lower quality to reduce size
        .toBuffer();

      console.log('✅ Image resized using Sharp');
      return resizedBuffer;
    } catch (sharpError) {
      console.log('⚠️ Sharp not available, using fallback approach');

      // Fallback: Just truncate the buffer (not ideal but will work)
      // This is a very basic approach - in production you'd want proper image resizing
      const maxSize = 15000; // Leave some buffer under 16KB
      if (imageBuffer.length > maxSize) {
        console.log('⚠️ Using buffer truncation fallback (not ideal)');
        return imageBuffer.slice(0, maxSize);
      }

      return imageBuffer;
    }
  }
}

module.exports = new OpenAIService();
