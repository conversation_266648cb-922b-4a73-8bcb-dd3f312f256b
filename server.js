const express = require('express');
const path = require('path');
const cron = require('node-cron');
require('dotenv').config();

const promptRoutes = require('./routes/prompts');
const scheduler = require('./services/scheduler');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static files with proper cache control
app.use(express.static(path.join(__dirname, 'public'), {
  maxAge: 0, // No caching for development
  etag: false,
  lastModified: false
}));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Routes
app.use('/', promptRoutes);

// Start scheduler if enabled
if (process.env.SCHEDULER_ENABLED === 'true') {
  const cronPattern = process.env.SCHEDULER_CRON || '0 */6 * * *';
  console.log(`🕐 Scheduler enabled with pattern: ${cronPattern}`);
  
  cron.schedule(cronPattern, async () => {
    console.log('🚀 Running scheduled image generation...');
    try {
      await scheduler.processNextPrompt();
    } catch (error) {
      console.error('❌ Scheduler error:', error);
    }
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).render('error', { error: err.message });
});

// 404 handler
app.use((req, res) => {
  res.status(404).render('error', { error: 'Page not found' });
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📁 Upload directory: ${path.join(__dirname, 'uploads')}`);
  console.log(`🖼️ Generated images directory: ${path.join(__dirname, 'generated-images')}`);
});
