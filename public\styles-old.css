/* AI Image Generator Styles - Modern Beautiful Design */
:root {
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --primary-light: #e0e7ff;
  --secondary-color: #10b981;
  --secondary-hover: #059669;
  --secondary-light: #d1fae5;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --danger-light: #fee2e2;
  --warning-color: #f59e0b;
  --warning-hover: #d97706;
  --warning-light: #fef3c7;
  --info-color: #3b82f6;
  --info-hover: #2563eb;
  --info-light: #dbeafe;
  --success-color: #10b981;
  --success-light: #d1fae5;

  /* Background & Surface Colors */
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --background-alt: #f8fafc;
  --card-background: #ffffff;
  --card-hover: #fefefe;
  --surface-1: #f9fafb;
  --surface-2: #f3f4f6;
  --surface-3: #e5e7eb;

  /* Text Colors */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;

  /* Border & Effects */
  --border-color: #e5e7eb;
  --border-hover: #d1d5db;
  --border-focus: var(--primary-color);
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.25s ease-out;
  --transition-slow: 0.35s ease-out;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: var(--background);
  color: var(--text-primary);
  line-height: 1.6;
  font-size: var(--font-size-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background);
  z-index: -1;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  position: relative;
}

.container-sm {
  max-width: 800px;
}

.container-lg {
  max-width: 1600px;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: var(--space-lg) 0;
  margin-bottom: var(--space-2xl);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-sm);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: var(--font-size-2xl);
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  transition: all var(--transition-normal);
}

.logo:hover {
  transform: translateY(-2px);
}

.nav {
  display: flex;
  gap: var(--space-xs);
  background: var(--surface-1);
  padding: var(--space-xs);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-inner);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--border-radius);
  transition: all var(--transition-normal);
  font-weight: 500;
  font-size: var(--font-size-sm);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left var(--transition-slow);
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link:hover,
.nav-link.active {
  background: var(--primary-color);
  color: var(--text-inverse);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

/* Cards */
.card {
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--space-xl);
  margin-bottom: var(--space-xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 2px solid var(--surface-2);
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

/* Grid Layout */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-file {
  width: 100%;
  padding: 0.75rem;
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;
}

.form-file:hover {
  border-color: var(--primary-color);
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-checkbox input {
  width: auto;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
  box-shadow: var(--shadow);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: var(--text-inverse);
  border: 1px solid var(--primary-color);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
  border-color: var(--primary-hover);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-hover) 100%);
  color: var(--text-inverse);
  border: 1px solid var(--secondary-color);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--secondary-hover) 0%, var(--secondary-color) 100%);
  border-color: var(--secondary-hover);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
  color: var(--text-inverse);
  border: 1px solid var(--danger-color);
}

.btn-danger:hover {
  background: linear-gradient(135deg, var(--danger-hover) 0%, var(--danger-color) 100%);
  border-color: var(--danger-hover);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-hover) 100%);
  color: var(--text-inverse);
  border: 1px solid var(--warning-color);
}

.btn-warning:hover {
  background: linear-gradient(135deg, var(--warning-hover) 0%, var(--warning-color) 100%);
  border-color: var(--warning-hover);
}

.btn-info {
  background: linear-gradient(135deg, var(--info-color) 0%, var(--info-hover) 100%);
  color: var(--text-inverse);
  border: 1px solid var(--info-color);
}

.btn-info:hover {
  background: linear-gradient(135deg, var(--info-hover) 0%, var(--info-color) 100%);
  border-color: var(--info-hover);
}

.btn-outline {
  background: var(--card-background);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
}

.btn-outline:hover {
  background: var(--surface-1);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-ghost {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  box-shadow: none;
}

.btn-ghost:hover {
  background: var(--surface-1);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
}

.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  gap: var(--space-xs);
}

.btn-lg {
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--font-size-lg);
  gap: var(--space-md);
}

.btn-xl {
  padding: var(--space-xl) var(--space-2xl);
  font-size: var(--font-size-xl);
  gap: var(--space-md);
  border-radius: var(--border-radius-lg);
}

/* Prompt Card */
.prompt-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: box-shadow 0.2s;
}

.prompt-card:hover {
  box-shadow: var(--shadow-lg);
}

.prompt-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  background: var(--background);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.prompt-content {
  padding: 1rem;
}

.prompt-text {
  font-size: 0.875rem;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prompt-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.prompt-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Status badges */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background: #d1fae5;
  color: #065f46;
}

.badge-danger {
  background: #fee2e2;
  color: #991b1b;
}

.badge-warning {
  background: #fef3c7;
  color: #92400e;
}

.badge-info {
  background: #dbeafe;
  color: #1e40af;
}

/* Stats */
.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--card-background);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* History */
.history-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: 0.5rem;
}

.history-image {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.history-content {
  flex: 1;
}

.history-prompt {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.history-meta {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Loading */
.loading {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Alerts */
.alert {
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

.alert-success {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.alert-error {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.alert-warning {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.alert-info {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

/* Enhanced UI Components */
.page-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
  padding: var(--space-2xl) 0;
}

.page-title {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
}

.page-icon {
  font-size: var(--font-size-4xl);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

/* Method Tabs */
.method-tabs {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-2xl);
  background: var(--surface-1);
  padding: var(--space-sm);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-inner);
}

.method-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  border: none;
  border-radius: var(--border-radius);
  background: transparent;
  color: var(--text-secondary);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
}

.method-tab.active {
  background: var(--card-background);
  color: var(--primary-color);
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.tab-icon {
  font-size: var(--font-size-lg);
}

/* Method Content */
.method-content {
  display: none;
}

.method-content.active {
  display: block;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Form Elements */
.form-label {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-sm);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

.label-icon {
  font-size: var(--font-size-lg);
}

.form-textarea.enhanced,
.form-input.enhanced {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--space-md);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  background: var(--card-background);
  resize: vertical;
}

.form-textarea.enhanced:focus,
.form-input.enhanced:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light);
  outline: none;
  transform: translateY(-1px);
}

.form-help {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-top: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: var(--info-light);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  color: var(--info-color);
}

.help-icon {
  font-size: var(--font-size-base);
}

/* Enhanced File Upload */
.form-file.enhanced {
  border: 3px dashed var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--space-2xl);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  background: var(--surface-1);
  position: relative;
  overflow: hidden;
}

.form-file.enhanced:hover,
.form-file.enhanced.drag-over {
  border-color: var(--primary-color);
  background: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.file-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
}

.file-upload-icon {
  font-size: var(--font-size-4xl);
  opacity: 0.7;
}

.file-upload-text {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
}

.file-upload-formats {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Enhanced Checkbox */
.form-checkbox.enhanced {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  background: var(--surface-1);
  border-radius: var(--border-radius);
  border: 2px solid var(--border-color);
  transition: all var(--transition-normal);
}

.form-checkbox.enhanced:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-weight: 500;
  cursor: pointer;
  margin: 0;
}

.checkbox-icon {
  font-size: var(--font-size-lg);
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-md);
  }

  .header-content {
    flex-direction: column;
    gap: var(--space-md);
  }

  .nav {
    flex-wrap: wrap;
    justify-content: center;
  }

  .grid-2,
  .grid-3 {
    grid-template-columns: 1fr;
  }

  .stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .prompt-actions {
    flex-direction: column;
  }

  .method-tabs {
    flex-direction: column;
  }

  .page-title {
    font-size: var(--font-size-3xl);
    flex-direction: column;
    gap: var(--space-sm);
  }

  .page-icon {
    font-size: var(--font-size-3xl);
  }
}

/* Image Preview */
.image-preview {
  margin-top: var(--space-lg);
  padding: var(--space-lg);
  background: var(--surface-1);
  border-radius: var(--border-radius-lg);
  border: 2px solid var(--border-color);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-sm);
  border-bottom: 2px solid var(--border-color);
}

.preview-title {
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.preview-image {
  width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

/* JSON Preview */
.json-preview {
  margin-top: var(--space-lg);
  padding: var(--space-lg);
  background: var(--surface-1);
  border-radius: var(--border-radius-lg);
  border: 2px solid var(--border-color);
}

.json-stats {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-md);
  padding: var(--space-md);
  background: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-value {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
  font-weight: 700;
}

.json-content {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--space-md);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--font-size-sm);
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
  color: var(--text-primary);
  white-space: pre-wrap;
}

/* JSON Format Help */
.json-format-help {
  margin-top: var(--space-lg);
  padding: var(--space-lg);
  background: var(--warning-light);
  border-radius: var(--border-radius-lg);
  border-left: 4px solid var(--warning-color);
}

.help-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.json-format-help h4 {
  margin: 0;
  color: var(--warning-color);
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.format-notes {
  margin-top: var(--space-md);
  padding: var(--space-md);
  background: var(--card-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.format-notes p {
  margin-bottom: var(--space-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.format-notes ul {
  margin: 0;
  padding-left: var(--space-lg);
  color: var(--text-secondary);
}

.format-notes li {
  margin-bottom: var(--space-xs);
  line-height: 1.5;
}

.json-example {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--space-md);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--text-primary);
  margin: 0;
  overflow-x: auto;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-2xl);
  padding-top: var(--space-lg);
  border-top: 2px solid var(--surface-2);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.flex {
  display: flex;
}

.gap-2 {
  gap: var(--space-sm);
}

.mb-0 {
  margin-bottom: 0 !important;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Dashboard Enhancements */
.dashboard-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
  padding: var(--space-2xl) 0;
}

.dashboard-title {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
}

.dashboard-icon {
  font-size: var(--font-size-4xl);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.dashboard-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

/* Enhanced Stats */
.stats.enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.stat-card {
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card.primary::before {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.stat-card.success::before {
  background: linear-gradient(90deg, var(--secondary-color), var(--secondary-hover));
}

.stat-card.info::before {
  background: linear-gradient(90deg, var(--info-color), var(--info-hover));
}

.stat-card.warning::before {
  background: linear-gradient(90deg, var(--warning-color), var(--warning-hover));
}

.stat-icon {
  font-size: var(--font-size-3xl);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-lg);
  background: var(--surface-1);
  flex-shrink: 0;
}

.stat-card.primary .stat-icon {
  background: var(--primary-light);
  color: var(--primary-color);
}

.stat-card.success .stat-icon {
  background: var(--secondary-light);
  color: var(--secondary-color);
}

.stat-card.info .stat-icon {
  background: var(--info-light);
  color: var(--info-color);
}

.stat-card.warning .stat-icon {
  background: var(--warning-light);
  color: var(--warning-color);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: 800;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
}

.trend-indicator {
  font-size: var(--font-size-lg);
  font-weight: 700;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius);
  background: var(--surface-1);
}

.trend-indicator.positive {
  color: var(--secondary-color);
  background: var(--secondary-light);
}

.trend-indicator.negative {
  color: var(--danger-color);
  background: var(--danger-light);
}

.trend-indicator.neutral {
  color: var(--text-secondary);
  background: var(--surface-2);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: var(--space-xl);
  right: var(--space-xl);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.toast {
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: var(--space-md) var(--space-lg);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--primary-color);
  min-width: 300px;
  max-width: 400px;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  animation: slideInRight 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.toast.success {
  border-left-color: var(--success-color);
}

.toast.error {
  border-left-color: var(--danger-color);
}

.toast.warning {
  border-left-color: var(--warning-color);
}

.toast.info {
  border-left-color: var(--info-color);
}

.toast-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.toast.success .toast-icon {
  color: var(--success-color);
}

.toast.error .toast-icon {
  color: var(--danger-color);
}

.toast.warning .toast-icon {
  color: var(--warning-color);
}

.toast.info .toast-icon {
  color: var(--info-color);
}

.toast-content {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.toast-close:hover {
  background: var(--surface-1);
  color: var(--text-primary);
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: var(--primary-color);
  animation: toastProgress 5s linear forwards;
}

.toast.success .toast-progress {
  background: var(--success-color);
}

.toast.error .toast-progress {
  background: var(--danger-color);
}

.toast.warning .toast-progress {
  background: var(--warning-color);
}

.toast.info .toast-progress {
  background: var(--info-color);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes toastProgress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Utilities */
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: 0.5rem;
}
.mb-2 {
  margin-bottom: 1rem;
}
.mt-1 {
  margin-top: 0.5rem;
}
.mt-2 {
  margin-top: 1rem;
}
.hidden {
  display: none;
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.5rem;
}
.gap-2 {
  gap: 1rem;
}

/* Additional utilities */
.text-left {
  text-align: left;
}
.text-secondary {
  color: var(--text-secondary) !important;
}
.text-primary {
  color: var(--text-primary) !important;
}
.p-4 {
  padding: 1rem;
}
.rounded {
  border-radius: var(--border-radius);
}
.shadow {
  box-shadow: var(--shadow);
}
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}

/* Alert styles for compatibility */
.alert {
  padding: var(--space-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--space-md);
  border: 1px solid transparent;
  display: block;
}

.alert-success {
  background-color: var(--success-light);
  border-color: var(--success-color);
  color: var(--success-color);
}

.alert-error {
  background-color: var(--danger-light);
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.alert-warning {
  background-color: var(--warning-light);
  border-color: var(--warning-color);
  color: var(--warning-color);
}

.alert-info {
  background-color: var(--info-light);
  border-color: var(--info-color);
  color: var(--info-color);
}
