const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');
const openaiService = require('../services/openai');
const cloudinaryService = require('../services/cloudinary');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Helper function to read prompts data
async function readPromptsData() {
  const dataPath = path.join(__dirname, '..', 'data', 'prompts.json');
  try {
    const data = await fs.readFile(dataPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading prompts data:', error);
    return {
      prompts: [],
      generationHistory: [],
      settings: {
        currentIndex: 0,
        lastGeneration: null,
        totalGenerations: 0,
        dailyGenerations: 0,
        lastResetDate: null
      }
    };
  }
}

// Helper function to write prompts data
async function writePromptsData(data) {
  const dataPath = path.join(__dirname, '..', 'data', 'prompts.json');
  await fs.writeFile(dataPath, JSON.stringify(data, null, 2));
}

// Home page - List all prompts
router.get('/', async (req, res) => {
  try {
    const data = await readPromptsData();
    res.render('index', { 
      prompts: data.prompts, 
      generationHistory: data.generationHistory.slice(-10), // Last 10 generations
      settings: data.settings 
    });
  } catch (error) {
    console.error('Error loading home page:', error);
    res.status(500).render('error', { error: 'Failed to load prompts' });
  }
});

// CSS test page
router.get('/css-test', (req, res) => {
  res.render('css-test', { title: 'CSS Test', layout: false });
});

// Add new prompt page
router.get('/add', (req, res) => {
  res.render('addPrompt');
});

// Handle adding new prompt
router.post('/add', upload.single('referenceImage'), async (req, res) => {
  try {
    const { text, enabled } = req.body;
    
    if (!text || text.trim() === '') {
      return res.status(400).render('error', { error: 'Prompt text is required' });
    }

    const data = await readPromptsData();
    
    const newPrompt = {
      id: uuidv4(),
      text: text.trim(),
      referenceImage: req.file ? req.file.filename : null,
      enabled: enabled === 'on',
      createdAt: new Date().toISOString(),
      lastUsed: null,
      timesUsed: 0
    };

    data.prompts.push(newPrompt);
    await writePromptsData(data);

    console.log('✅ New prompt added:', newPrompt.text);
    res.redirect('/?success=Prompt added successfully');
  } catch (error) {
    console.error('Error adding prompt:', error);
    res.status(500).render('error', { error: 'Failed to add prompt' });
  }
});

// Add bulk prompts from JSON
router.post('/add-bulk', upload.single('jsonFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).render('error', { error: 'JSON file is required' });
    }

    // Read and parse the JSON file
    const jsonContent = await fs.readFile(req.file.path, 'utf8');
    let jsonData;

    try {
      jsonData = JSON.parse(jsonContent);
    } catch (parseError) {
      // Clean up uploaded file
      await fs.remove(req.file.path);
      return res.status(400).render('error', { error: 'Invalid JSON format' });
    }

    // Validate JSON structure
    if (!jsonData.prompts || !Array.isArray(jsonData.prompts)) {
      await fs.remove(req.file.path);
      return res.status(400).render('error', { error: 'JSON must contain a "prompts" array' });
    }

    if (jsonData.prompts.length === 0) {
      await fs.remove(req.file.path);
      return res.status(400).render('error', { error: 'No prompts found in JSON file' });
    }

    // Validate each prompt
    const validPrompts = [];
    const errors = [];

    jsonData.prompts.forEach((prompt, index) => {
      if (!prompt.text || typeof prompt.text !== 'string' || prompt.text.trim() === '') {
        errors.push(`Prompt ${index + 1}: Missing or invalid text`);
        return;
      }

      validPrompts.push({
        id: uuidv4(),
        text: prompt.text.trim(),
        referenceImage: null, // Bulk upload doesn't support reference images yet
        enabled: prompt.enabled !== false, // Default to true unless explicitly false
        createdAt: new Date().toISOString(),
        lastUsed: null,
        timesUsed: 0
      });
    });

    if (errors.length > 0) {
      await fs.remove(req.file.path);
      return res.status(400).render('error', {
        error: `Validation errors:\n${errors.join('\n')}`
      });
    }

    // Add prompts to database
    const data = await readPromptsData();
    data.prompts.push(...validPrompts);
    await writePromptsData(data);

    // Clean up uploaded file
    await fs.remove(req.file.path);

    console.log(`✅ ${validPrompts.length} prompts added from JSON file`);
    res.redirect(`/?success=${validPrompts.length} prompts added successfully from JSON file`);
  } catch (error) {
    console.error('Error adding bulk prompts:', error);

    // Clean up uploaded file if it exists
    if (req.file && req.file.path) {
      try {
        await fs.remove(req.file.path);
      } catch (cleanupError) {
        console.error('Error cleaning up file:', cleanupError);
      }
    }

    res.status(500).render('error', { error: 'Failed to add prompts from JSON file' });
  }
});

// Edit prompt page
router.get('/edit/:id', async (req, res) => {
  try {
    const data = await readPromptsData();
    const prompt = data.prompts.find(p => p.id === req.params.id);
    
    if (!prompt) {
      return res.status(404).render('error', { error: 'Prompt not found' });
    }

    res.render('editPrompt', { prompt });
  } catch (error) {
    console.error('Error loading edit page:', error);
    res.status(500).render('error', { error: 'Failed to load prompt' });
  }
});

// Handle editing prompt
router.post('/edit/:id', upload.single('referenceImage'), async (req, res) => {
  try {
    const { text, enabled, removeImage } = req.body;
    const data = await readPromptsData();
    const promptIndex = data.prompts.findIndex(p => p.id === req.params.id);
    
    if (promptIndex === -1) {
      return res.status(404).render('error', { error: 'Prompt not found' });
    }

    const prompt = data.prompts[promptIndex];
    
    // Update prompt text
    if (text && text.trim() !== '') {
      prompt.text = text.trim();
    }
    
    // Update enabled status
    prompt.enabled = enabled === 'on';
    
    // Handle image updates
    if (removeImage === 'on' && prompt.referenceImage) {
      // Remove old image file
      const oldImagePath = path.join(__dirname, '..', 'uploads', prompt.referenceImage);
      if (await fs.pathExists(oldImagePath)) {
        await fs.unlink(oldImagePath);
      }
      prompt.referenceImage = null;
    }
    
    if (req.file) {
      // Remove old image if exists
      if (prompt.referenceImage) {
        const oldImagePath = path.join(__dirname, '..', 'uploads', prompt.referenceImage);
        if (await fs.pathExists(oldImagePath)) {
          await fs.unlink(oldImagePath);
        }
      }
      prompt.referenceImage = req.file.filename;
    }
    
    prompt.updatedAt = new Date().toISOString();
    await writePromptsData(data);

    console.log('✅ Prompt updated:', prompt.text);
    res.redirect('/');
  } catch (error) {
    console.error('Error updating prompt:', error);
    res.status(500).render('error', { error: 'Failed to update prompt' });
  }
});

// Delete prompt
router.post('/delete/:id', async (req, res) => {
  try {
    const data = await readPromptsData();
    const promptIndex = data.prompts.findIndex(p => p.id === req.params.id);
    
    if (promptIndex === -1) {
      return res.status(404).render('error', { error: 'Prompt not found' });
    }

    const prompt = data.prompts[promptIndex];
    
    // Remove reference image if exists
    if (prompt.referenceImage) {
      const imagePath = path.join(__dirname, '..', 'uploads', prompt.referenceImage);
      if (await fs.pathExists(imagePath)) {
        await fs.unlink(imagePath);
      }
    }
    
    data.prompts.splice(promptIndex, 1);
    await writePromptsData(data);

    console.log('✅ Prompt deleted:', prompt.text);
    res.redirect('/');
  } catch (error) {
    console.error('Error deleting prompt:', error);
    res.status(500).render('error', { error: 'Failed to delete prompt' });
  }
});

// Toggle prompt enabled status
router.post('/toggle/:id', async (req, res) => {
  try {
    const data = await readPromptsData();
    const prompt = data.prompts.find(p => p.id === req.params.id);
    
    if (!prompt) {
      return res.status(404).json({ error: 'Prompt not found' });
    }

    prompt.enabled = !prompt.enabled;
    await writePromptsData(data);

    console.log('✅ Prompt toggled:', prompt.text, '- Enabled:', prompt.enabled);
    res.json({ success: true, enabled: prompt.enabled });
  } catch (error) {
    console.error('Error toggling prompt:', error);
    res.status(500).json({ error: 'Failed to toggle prompt' });
  }
});

// Manual generation trigger
router.post('/generate/:id', async (req, res) => {
  try {
    const data = await readPromptsData();
    const prompt = data.prompts.find(p => p.id === req.params.id);
    
    if (!prompt) {
      return res.status(404).json({ error: 'Prompt not found' });
    }

    console.log('🚀 Manual generation triggered for prompt:', prompt.text);
    
    // Generate image
    const result = await openaiService.generateImageFromPromptData(prompt);
    
    if (result.success) {
      // Try to upload to Cloudinary (optional)
      let cloudinaryResult = null;
      try {
        cloudinaryResult = await cloudinaryService.uploadImage(result.filepath, {
          prompt: prompt.text,
          referenceImage: prompt.referenceImage
        });
        console.log('☁️ Cloudinary upload successful');
      } catch (cloudinaryError) {
        console.log('⚠️ Cloudinary upload failed (continuing without it):', cloudinaryError.message);
      }

      // Update generation history
      const generationRecord = {
        id: uuidv4(),
        promptId: prompt.id,
        promptText: prompt.text,
        generatedImage: result.filename,
        cloudinaryUrl: cloudinaryResult?.url || null,
        cloudinaryPublicId: cloudinaryResult?.public_id || null,
        generatedAt: new Date().toISOString(),
        usage: result.usage,
        manual: true,
        usedReferenceImage: result.usedReferenceImage || false
      };

      data.generationHistory.push(generationRecord);

      // Update prompt statistics
      prompt.lastUsed = new Date().toISOString();
      prompt.timesUsed += 1;

      // Update settings
      data.settings.totalGenerations += 1;
      data.settings.dailyGenerations += 1;
      data.settings.lastGeneration = new Date().toISOString();

      await writePromptsData(data);

      console.log('✅ Manual generation completed');
      res.json({
        success: true,
        message: 'Image generated successfully',
        cloudinaryUrl: cloudinaryResult?.url || null,
        localImage: result.filename,
        usedReferenceImage: result.usedReferenceImage || false
      });
    } else {
      res.status(500).json({ error: 'Failed to generate image' });
    }
  } catch (error) {
    console.error('Error in manual generation:', error);
    res.status(500).json({ error: 'Generation failed: ' + error.message });
  }
});

// View generation history
router.get('/history', async (req, res) => {
  try {
    const data = await readPromptsData();
    res.render('history', { 
      history: data.generationHistory.reverse(),
      settings: data.settings 
    });
  } catch (error) {
    console.error('Error loading history:', error);
    res.status(500).render('error', { error: 'Failed to load history' });
  }
});

// Settings page
router.get('/settings', async (req, res) => {
  try {
    const data = await readPromptsData();
    res.render('settings', { 
      settings: data.settings,
      env: {
        SCHEDULER_ENABLED: process.env.SCHEDULER_ENABLED,
        SCHEDULER_CRON: process.env.SCHEDULER_CRON,
        MAX_GENERATIONS_PER_DAY: process.env.MAX_GENERATIONS_PER_DAY,
        IMAGE_SIZE: process.env.IMAGE_SIZE,
        IMAGE_QUALITY: process.env.IMAGE_QUALITY,
        IMAGE_FORMAT: process.env.IMAGE_FORMAT
      }
    });
  } catch (error) {
    console.error('Error loading settings:', error);
    res.status(500).render('error', { error: 'Failed to load settings' });
  }
});

// Test API endpoint
router.post('/test-api', async (req, res) => {
  try {
    console.log('🧪 Testing API with simple prompt...');

    const testPrompt = {
      text: 'A beautiful sunset over mountains with vibrant colors',
      referenceImage: null
    };

    const result = await openaiService.generateImageFromPromptData(testPrompt);

    if (result.success) {
      console.log('✅ API test successful');
      res.json({
        success: true,
        message: 'API is working correctly',
        filename: result.filename,
        usage: result.usage
      });
    } else {
      res.status(500).json({ error: 'API test failed' });
    }
  } catch (error) {
    console.error('❌ API test failed:', error);
    res.status(500).json({
      error: 'API test failed',
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

module.exports = router;
